<odoo>
    <record id="res_config_settings_view_form_inherit_req" model="ir.ui.view">
        <field name="name">res.config.settings.custom.view.form.inherit.hr.attendance.req</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="hr_attendance.res_config_settings_view_form"/>
        <field name="arch" type="xml">

            <xpath expr="//div[div/field[@name='is_late_tolerance']]" position="after">
                <div class="col-12 col-lg-6 o_setting_box mt16" title="Batasi Pengajuan Absensi">
                    <div class="o_setting_left_pane">
                        <field name="is_limit_attendance_request"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <span class="o_form_label">Limit Pengajuan Absensi</span>
                        <div class="text-muted">Batasi jumlah hari maksimal pengajuan absensi setelah tanggal cut-off.</div>
                        <label for="limit_attendance_request" attrs="{'invisible':[('is_limit_attendance_request','!=',True)]}"/>
                        <field name="limit_attendance_request" attrs="{'invisible':[('is_limit_attendance_request','!=',True)]}"/>
                        <span class="o_form_label" attrs="{'invisible':[('is_limit_attendance_request','!=',True)]}">Hari</span>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>














<!-- <odoo>
<record id="res_config_settings_view_form_inherit_req" model="ir.ui.view">
    <field name="name">res.config.settings.custom.view.form.inherit.hr.attendance.req</field>
    <field name="model">res.config.settings</field>
    <field name="inherit_id" ref="hr_attendance.res_config_settings_view_form"/>
    <field name="arch" type="xml">

        <xpath expr="//div[div/field[@name='is_late_tolerance']]" position="replace">
            <div class="col-12 col-lg-6 o_setting_box mt16" title="Default for Late Tolerance in Attendance">
                <div class="o_setting_left_pane">
                    <field name="is_late_tolerance"/>
                </div>
                <div class="o_setting_right_pane">
                    <label for="minute_tolerance" class="o_form_label">Late Tolerance</label>
                    <div class="text-muted">Default for Late Tolerance in Attendance</div>
                    <field name="minute_tolerance" attrs="{'invisible': [('is_late_tolerance', '!=', True)]}"/>
                    <span class="o_form_label" attrs="{'invisible': [('is_late_tolerance', '!=', True)]}">Minutes</span>
                </div>
            </div>
        </xpath>

        <xpath expr="//div[div/field[@name='is_late_tolerance']]" position="after">
            <div class="col-12 col-lg-6 o_setting_box" title="Batasi Pengajuan Absensi">
                <div class="o_setting_left_pane">
                    <field name="is_limit_attendance_request"/>
                </div>
                <div class="o_setting_right_pane">
                    <span class="o_form_label">Limit Pengajuan Absensi</span>
                    <div class="text-muted">Batasi jumlah hari maksimal pengajuan absensi setelah tanggal cut-off.</div>
                    <label for="limit_attendance_request" attrs="{'invisible':[('is_limit_attendance_request','!=',True)]}"/>
                    <field name="limit_attendance_request" attrs="{'invisible':[('is_limit_attendance_request','!=',True)]}"/>
                    <span class="o_form_label" attrs="{'invisible':[('is_limit_attendance_request','!=',True)]}">Hari</span>
                </div>
            </div>
        </xpath>

    </field>
</record>
</odoo> -->