# SP Deduction Calculation - Final Version
# This code should be used in the "Python Code" field of the SP Deduction salary rule

# Check if employee has SP deduction
if not payslip.has_sp_deduction:
    result = 0.0
else:
    # Step 1: Calculate total earnings (Basic + Allowance)
    # PERBAIKAN: Gunakan name bukan code karena lebih konsisten
    total_earnings = 0.0
    for line in payslip.line_ids:
        if line.category_id.name in ['Basic', 'Allowance']:
            total_earnings += line.total

    # Step 2: Calculate total deductions (excluding SP deduction itself)
    # PERBAIKAN: Gunakan code yang sesuai dengan sistem Anda (D10 atau SP_DED)
    total_deductions = 0.0
    for line in payslip.line_ids:
        if line.category_id.name == 'Deduction' and line.code != 'D10':
            total_deductions += line.total

    # Step 3: Base amount for SP calculation = Total Earnings - Other Deductions
    # Note: deductions are negative values, so we add them
    base_amount = total_earnings + total_deductions

    # Step 4: Handle different employment types
    if contract.employment_state == 'part_time':
        # For part-time employees: calculate based on working days
        working_days = 0
        for slip in payslip.worked_days_line_ids:
            if slip.sequence == 1:  # Main working days entry
                working_days = slip.number_of_days if slip.number_of_days else 0

        # Adjust base amount proportionally for part-time
        if working_days > 0:
            base_amount = base_amount * working_days / 30
    else:
        # For non-part-time employees: check for prorate conditions
        prorate = False
        join_date = contract.employee_id.join_date
        resign_date = contract.employee_id.resign_date if contract.employee_id.resign_date else contract.date_end
        date_start = payslip.date_from
        date_to = payslip.date_to

        # Determine if prorate calculation is needed
        if ((date_start < join_date < date_to) or (join_date > date_to) or
            (resign_date and date_start < resign_date < date_to) or
            (resign_date and resign_date <= date_start)):
            prorate = True

        if prorate:
            # Get department's schedule prorate (default working days per month)
            schedule_prorate = contract.employee_id.department_id.schedule_prorate
            schedule_prorate = schedule_prorate if schedule_prorate > 0 else 1

            # Get actual working days
            working_days = 0
            for slip in payslip.worked_days_line_ids:
                if slip.sequence == 1:  # Main working days entry
                    working_days = slip.number_of_days if slip.number_of_days else 0

            # Adjust base amount proportionally for prorate
            if working_days > 0 and schedule_prorate > 0:
                prorate_factor = working_days / schedule_prorate
                base_amount = base_amount * prorate_factor
                # Ensure base amount doesn't exceed the original amount
                original_amount = total_earnings + total_deductions
                base_amount = base_amount if base_amount < original_amount else original_amount

        # For full-time non-prorate employees: use base_amount as is

    # Step 5: Calculate SP deduction
    # Apply the SP deduction percentage to the calculated base amount
    result = -1 * (base_amount * payslip.sp_deduction_percentage / 100)

# Notes:
# 1. This calculation ensures SP deduction is based on net earnings after other deductions
# 2. Part-time employees get proportional deduction based on working days
# 3. Prorate employees get proportional deduction based on department schedule
# 4. Full-time employees get deduction based on full net earnings
# 5. The result is negative because it's a deduction
