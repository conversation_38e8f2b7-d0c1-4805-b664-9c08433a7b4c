# SP Deduction Calculation - Changelog

## Perubahan yang Dilakukan

### Sebelumnya
SP Deduction dihitung langsung dari `contract.wage` tanpa mempertimbangkan:
- Total earnings yang sebenarnya (Basic + Allowance)
- Deduction lain yang sudah ada
- Kondisi employment yang berbeda (part-time, prorate, full-time)

```python
# Kode lama
result = -1 * (contract.wage * (payslip.sp_deduction_percentage / 100))
```

### Setelah Perbaikan
SP Deduction sekarang dihitung berdasarkan:
1. **Total Earnings** (Basic + Allowance) 
2. **Dikurangi deduction lain** (selain SP deduction itu sendiri)
3. **Disesuaikan dengan kondisi employment**:
   - **Part-time**: Proporsional berdasarkan working days
   - **Prorate**: Proporsional berdasarkan department schedule_prorate
   - **Full-time**: Menggunakan full amount

## Formula Perhitungan

### 1. Hitung Total Earnings
```python
total_earnings = sum(line.total for line in payslip.line_ids 
                    if line.category_id.code in ['BASIC', 'ALW'])
```

### 2. Hitung Total Deductions (selain SP)
```python
total_deductions = sum(line.total for line in payslip.line_ids 
                      if line.category_id.code == 'DED' and line.code != 'SP_DED')
```

### 3. Base Amount
```python
base_amount = total_earnings + total_deductions  # deductions bernilai negatif
```

### 4. Adjustment berdasarkan Employment Type

#### Part-time
```python
if contract.employment_state == 'part_time':
    working_days = get_working_days_from_payslip()
    base_amount = base_amount * working_days / 30
```

#### Prorate (bukan part-time tapi ada kondisi prorate)
```python
if prorate_condition_met:
    schedule_prorate = contract.employee_id.department_id.schedule_prorate
    working_days = get_working_days_from_payslip()
    prorate_factor = working_days / schedule_prorate
    base_amount = base_amount * prorate_factor
```

#### Full-time (tidak ada adjustment)
```python
# base_amount tetap sama
```

### 5. Hitung SP Deduction
```python
result = -1 * (base_amount * payslip.sp_deduction_percentage / 100)
```

## Kondisi Prorate

Prorate akan diterapkan jika salah satu kondisi berikut terpenuhi:
- `date_start < join_date < date_to` (karyawan baru join di tengah periode)
- `join_date > date_to` (karyawan belum join)
- `date_start < resign_date < date_to` (karyawan resign di tengah periode)
- `resign_date <= date_start` (karyawan sudah resign)

## Contoh Perhitungan

### Kasus 1: Full-time Employee
- Basic Salary: 5,000,000
- Allowance: 1,000,000
- Other Deductions: -200,000
- SP Percentage: 10%

```
Total Earnings = 5,000,000 + 1,000,000 = 6,000,000
Base Amount = 6,000,000 + (-200,000) = 5,800,000
SP Deduction = -1 * (5,800,000 * 10/100) = -580,000
```

### Kasus 2: Part-time Employee (20 hari kerja)
- Basic Salary: 5,000,000
- Allowance: 1,000,000
- Other Deductions: -200,000
- SP Percentage: 10%
- Working Days: 20

```
Total Earnings = 5,000,000 + 1,000,000 = 6,000,000
Base Amount = 6,000,000 + (-200,000) = 5,800,000
Adjusted Base = 5,800,000 * 20/30 = 3,866,667
SP Deduction = -1 * (3,866,667 * 10/100) = -386,667
```

### Kasus 3: Prorate Employee (15 hari kerja, schedule_prorate = 23)
- Basic Salary: 5,000,000
- Allowance: 1,000,000
- Other Deductions: -200,000
- SP Percentage: 10%
- Working Days: 15
- Schedule Prorate: 23

```
Total Earnings = 5,000,000 + 1,000,000 = 6,000,000
Base Amount = 6,000,000 + (-200,000) = 5,800,000
Prorate Factor = 15/23 = 0.652
Adjusted Base = 5,800,000 * 0.652 = 3,782,609
SP Deduction = -1 * (3,782,609 * 10/100) = -378,261
```

## File yang Diubah

1. `custom/hr_disciplinary_action/README.md` - Updated documentation
2. `custom/hr_disciplinary_action/data/sp_deduction_salary_rule.xml` - New salary rule
3. `custom/hr_disciplinary_action/examples/sp_deduction_python_code.py` - Example code
4. `custom/hr_disciplinary_action/__manifest__.py` - Added new data file

## Implementasi

Untuk menggunakan kode yang sudah diperbaiki:

1. Buat salary rule baru dengan code `SP_DED`
2. Set category ke `Deduction`
3. Set condition ke `Python Expression`: `result = payslip.has_sp_deduction`
4. Set amount type ke `Python Code`
5. Copy kode dari file `examples/sp_deduction_python_code.py`

Atau install data file yang sudah disediakan di `data/sp_deduction_salary_rule.xml`.
