# -*- coding: utf-8 -*-

def migrate(cr, version):
    """
    Migration script to add sp_deduction_amount field to hr_payslip table
    """
    # Check if column exists
    cr.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name='hr_payslip' AND column_name='sp_deduction_amount'
    """)
    
    if not cr.fetchone():
        # Add the column if it doesn't exist
        cr.execute("""
            ALTER TABLE hr_payslip 
            ADD COLUMN sp_deduction_amount NUMERIC DEFAULT 0.0
        """)
        
        # Update existing records
        cr.execute("""
            UPDATE hr_payslip 
            SET sp_deduction_amount = 0.0 
            WHERE sp_deduction_amount IS NULL
        """)
        
        print("Added sp_deduction_amount column to hr_payslip table")
