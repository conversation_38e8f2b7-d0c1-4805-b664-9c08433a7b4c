# SP Deduction Implementation Guide - Odoo 14

## 🎯 Overview

Sistem SP Deduction yang telah direfactor untuk Odoo 14 dengan fitur:
- **Automatic Detection**: Otomatis mendeteksi employee yang memiliki SP/Indisciplinary Actions
- **Smart Calculation**: Perhitungan berdasarkan total earnings (Basic + Allowance) dikurangi deduction lain
- **Employment Type Support**: Mendukung part-time, prorate, dan full-time employees
- **Clean Architecture**: Pemisahan logic antara model dan salary rule

## 🚀 Cara Implementasi

### 1. Install Module
```bash
# Update module
python odoo-bin -u hr_disciplinary_action -d your_database

# Atau restart Odoo dan upgrade dari Apps menu
```

### 2. Setup Salary Rule (Otomatis)
Module akan otomatis membuat salary rule "SP Deduction" dengan:
- **Code**: SP_DED
- **Category**: Deduction
- **Condition**: `result = payslip.has_sp_deduction`
- **Amount**: Pre-calculated dari payslip model

### 3. Manual Setup (<PERSON><PERSON>)

Jika salary rule tidak terbuat otomatis:

1. **Buat Salary Rule Baru**:
   ```
   Name: SP Deduction
   Code: SP_DED
   Sequence: 200
   Category: Deduction
   Condition Based on: Python Expression
   Python Condition: result = payslip.has_sp_deduction
   Amount Type: Python Code
   ```

2. **Copy Python Code**:
   ```python
   if not payslip.has_sp_deduction:
       result = 0.0
   else:
       result = -1 * payslip.sp_deduction_amount
   ```

3. **Tambahkan ke Salary Structure**:
   - Masuk ke Payroll > Configuration > Salary Structures
   - Pilih structure yang digunakan
   - Tambahkan "SP Deduction" ke Salary Rules

## 📋 Cara Penggunaan

### 1. Buat Disciplinary Action
```
Payroll > Performance > Disciplinary Actions > Create

Fields:
- Employee: Pilih employee
- Type: Pilih type (misal: Late Coming)
- Action: SP I / SP II / SP III
- Additional Salary Deduction: ✓ (centang)
- Salary Deduction Percentage: 10% (contoh)
- Salary Deduction Validity: 2 (bulan)
- State: Submit > Approve
```

### 2. Generate Payslip
```
Payroll > Payslips > Create
- Pilih employee yang memiliki SP
- Compute Sheet
- Cek apakah line "SP Deduction" muncul
```

### 3. Verifikasi Hasil
- **Has SP**: Indicator di tree view payslip
- **SP Information**: Group di form view payslip
- **SP Deduction Line**: Muncul di payslip lines

## 🔍 Cara Kerja Sistem

### 1. Detection Logic
```python
# Sistem otomatis cek apakah employee memiliki SP aktif
domain = [
    ('employee_id', '=', payslip.employee_id.id),
    ('state', '=', 'approved'),
    ('effective_date', '<=', payslip.date_to),
    ('validity_end_date', '>=', payslip.date_from),
    ('additional_salary_deduction', '=', True),
    ('remaining_validity', '>', 0)
]
```

### 2. Calculation Logic
```python
# Base amount = Total Earnings - Other Deductions
total_earnings = Basic + Allowance
total_deductions = Other Deductions (excluding SP)
base_amount = total_earnings + total_deductions

# Employment type adjustment
if part_time:
    base_amount = base_amount * working_days / 30
elif prorate:
    base_amount = base_amount * (working_days / schedule_prorate)
# else: full-time, no adjustment

# Final calculation
sp_deduction = base_amount * sp_percentage / 100
```

## 🧪 Testing Scenarios

### Scenario 1: Full-time Employee
```
Employee: John Doe (full-time)
Basic: 5,000,000
Allowance: 1,000,000
Other Deductions: -200,000
SP: 10%

Expected:
- Base Amount: 5,800,000
- SP Deduction: -580,000
```

### Scenario 2: Part-time Employee
```
Employee: Jane Smith (part-time, 20 days)
Basic: 3,333,333 (20/30 * 5,000,000)
Allowance: 666,667 (20/30 * 1,000,000)
Other Deductions: -133,333
SP: 10%

Expected:
- Base Amount: 3,866,667
- SP Deduction: -386,667
```

### Scenario 3: Prorate Employee
```
Employee: Bob Wilson (prorate, 15 days, schedule_prorate=23)
Basic: Calculated based on prorate
SP: 10%

Expected:
- Base Amount: Adjusted by (15/23)
- SP Deduction: Proportional
```

## 🔧 Troubleshooting

### Problem: SP Deduction tidak muncul
**Solution**:
1. Cek apakah employee memiliki Disciplinary Action yang approved
2. Cek apakah Additional Salary Deduction = True
3. Cek apakah remaining_validity > 0
4. Cek apakah salary rule SP_DED ada di structure

### Problem: Perhitungan salah
**Solution**:
1. Cek kategori salary rules (Basic, Allowance, Deduction)
2. Cek employment_state employee
3. Cek working_days di payslip
4. Cek schedule_prorate di department

### Problem: Error saat compute payslip
**Solution**:
1. Cek log error di Settings > Technical > Logging
2. Pastikan semua field computed dengan benar
3. Restart Odoo service

## 📊 Fields Reference

### HR Payslip (New Fields)
- `has_sp_deduction`: Boolean, indicates if employee has active SP
- `sp_deduction_percentage`: Float, total percentage of SP deduction
- `sp_deduction_amount`: Float, calculated SP deduction amount
- `disciplinary_action_ids`: Many2many, active disciplinary actions

### HR Disciplinary Action (Existing)
- `additional_salary_deduction`: Boolean, enable salary deduction
- `salary_deduction_percentage`: Float, percentage to deduct
- `remaining_validity`: Integer, remaining months of validity

## 🎉 Benefits

1. **Automatic**: Tidak perlu manual input SP di payslip
2. **Accurate**: Perhitungan berdasarkan net earnings
3. **Flexible**: Support berbagai employment type
4. **Maintainable**: Clean separation of concerns
5. **Auditable**: Track disciplinary actions dan validity
