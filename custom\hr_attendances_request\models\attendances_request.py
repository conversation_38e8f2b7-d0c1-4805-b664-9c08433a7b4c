from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import timedelta, date
import logging

_logger = logging.getLogger(__name__)

class AttendancesRequest(models.Model):
    _inherit = 'attendance.request'

    def _get_cutoff_date(self):
        """<PERSON>bil tanggal cut-off berdasarkan logika 'tanggal 29'."""
        today_str = fields.Date.context_today(self)
        today = fields.Date.from_string(today_str)  # Convert string ke date object
        if today.day >= 29:
            cutoff = today.replace(day=29)
        else:
            if today.month == 1:
                cutoff = date(today.year - 1, 12, 29)
            else:
                cutoff = date(today.year, today.month - 1, 29)
        return cutoff

    @api.model
    def create(self, vals):
        params = self.env['ir.config_parameter'].sudo()
        is_limit_active = params.get_param('hr_attendances_request.is_limit_attendance_request') == 'True'
        limit_days = int(params.get_param('hr_attendances_request.limit_attendance_request', '0') or 0)

        if is_limit_active and limit_days > 0 and vals.get('date'):
            request_date = fields.Date.from_string(vals.get('date'))
            cutoff_date = self._get_cutoff_date()
            max_allowed_date = cutoff_date + timedelta(days=limit_days)

            _logger.info("CREATE Validasi | Request Date: %s | Cutoff: %s | Max Allowed: %s",
                        request_date, cutoff_date, max_allowed_date)

            if request_date > max_allowed_date:
                _logger.error("Validasi Error: request_date %s melebihi max_allowed_date %s", request_date, max_allowed_date)
                raise ValidationError(_(
                    "Pengajuan absensi melebihi batas %s hari setelah cut-off (%s).\nTanggal diajukan: %s"
                ) % (limit_days, cutoff_date.strftime('%d/%m/%Y'), request_date.strftime('%d/%m/%Y')))

        return super().create(vals)

    def action_request_approval(self):
        params = self.env['ir.config_parameter'].sudo()
        is_limit_active = params.get_param('hr_attendances_request.is_limit_attendance_request') == 'True'
        is_late_tolerance = params.get_param('hr_attendances_request.is_late_tolerance') == 'True'
        limit_days = int(params.get_param('hr_attendances_request.limit_attendance_request', '0') or 0)
        minute_tolerance = float(params.get_param('hr_attendances_request.minute_tolerance', '0') or 0)

        _logger.info("=== Attendance Request Approval Debug ===")
        _logger.info("Limit Active: %s, Limit Days: %s", is_limit_active, limit_days)
        _logger.info("Late Tolerance Active: %s, Minute Tolerance: %s", is_late_tolerance, minute_tolerance)

        for rec in self:
            if rec.state == 'approved':
                continue

            if not rec.date:
                raise ValidationError(_("Tanggal pengajuan absensi tidak boleh kosong."))

            request_date = rec.date
            cutoff_date = self._get_cutoff_date()
            max_allowed_date = cutoff_date + timedelta(days=limit_days)

            _logger.info("Request Date: %s, Cutoff: %s, Max Allowed: %s", request_date, cutoff_date, max_allowed_date)

            if is_limit_active and limit_days > 0:
                if request_date > max_allowed_date:
                    raise ValidationError(_(
                        "Batas maksimal pengajuan absensi adalah %s hari setelah tanggal cut-off %s.\n"
                        "Tanggal yang Anda ajukan: %s"
                    ) % (limit_days, cutoff_date.strftime('%d/%m/%Y'), request_date.strftime('%d/%m/%Y')))

            if is_late_tolerance and minute_tolerance > 0:
                if hasattr(rec, 'late_minutes') and rec.late_minutes is not None:
                    _logger.info("Late minutes: %s", rec.late_minutes)
                    if rec.late_minutes <= minute_tolerance:
                        rec.state = 'approved'
                        _logger.info("Auto-approved karena dalam toleransi keterlambatan")
                        continue

        return super().action_request_approval()
