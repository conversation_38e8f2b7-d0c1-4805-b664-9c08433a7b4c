# HR Disciplinary Action - Odoo 14

This module allows you to manage disciplinary actions (Surat Peringatan) and automatically apply salary deductions (Potongan THP) based on these actions.

## 🚀 Features

- **Disciplinary Management**: Create and manage disciplinary actions (SP I, SP II, SP III)
- **Automatic Detection**: Automatically detects employees with active SP when generating payslips
- **Smart Calculation**: SP deduction based on total earnings (Basic + Allowance) minus other deductions
- **Employment Type Support**: Supports part-time, prorate, and full-time employees
- **Approval Workflow**: Complete approval workflow for disciplinary actions
- **Validity Tracking**: Automatic calculation of remaining validity periods
- **Payroll Integration**: Seamless integration with payroll system
- **Hide/Unhide Support**: Payslip hide/unhide functionality affects remaining validity

## Configuration

1. Go to Performance > Configuration > Disciplinary Action Types to create different types of disciplinary actions.
2. The module automatically creates a salary rule for SP deductions.

## Usage

1. Go to Performance > Disciplinary Actions to create a new disciplinary action.
2. Fill in the required information:
   - Employee
   - Type
   - Action (SP I, SP II, SP III)
   - Validity Period
   - Additional Salary Deduction (if applicable)
   - Salary Deduction Percentage (if applicable)
   - Salary Deduction Validity (if applicable)
   - Note
   - Attachments (if any)
3. Submit the disciplinary action for approval.
4. Once approved, the disciplinary action becomes effective and will be considered in payroll calculations if salary deduction is enabled.
5. When processing payslips:
   - The system will automatically detect active disciplinary actions for the employee
   - You can hide/unhide payslips using the view_type_slip field
   - When a payslip is hidden, the remaining validity of associated disciplinary actions increases by 1
   - When a payslip is unhidden, the remaining validity decreases by 1

## Payroll Integration

To integrate with payroll, you need to create a salary rule manually:

1. Go to Payroll > Configuration > Salary Rules
2. Create a new salary rule with the following settings:
   - Name: SP Deduction
   - Code: SP_DED
   - Category: Deductions
   - Condition: Python Expression
   - Python Condition: `result = payslip.has_sp_deduction`
   - Amount Type: Python Code
   - Python Code:
     ```python
     if not payslip.has_sp_deduction:
         result = 0.0
     else:
         # Calculate total earnings (Basic + Allowance)
         total_earnings = 0.0
         for line in payslip.line_ids:
             if line.category_id.code in ['BASIC', 'ALW']:
                 total_earnings += line.total

         # Calculate total deductions (excluding SP deduction itself)
         total_deductions = 0.0
         for line in payslip.line_ids:
             if line.category_id.code == 'DED' and line.code != 'SP_DED':
                 total_deductions += line.total

         # Base amount for SP calculation = Total Earnings - Other Deductions
         base_amount = total_earnings + total_deductions  # deductions are negative

         # Handle different employment types
         if contract.employment_state == 'part_time':
             # For part-time: calculate based on working days
             working_days = 0
             for slip in payslip.worked_days_line_ids:
                 if slip.sequence == 1:
                     working_days = slip.number_of_days if slip.number_of_days else 0

             # Adjust base amount proportionally for part-time
             if working_days > 0:
                 base_amount = base_amount * working_days / 30
         else:
             # Check for prorate conditions
             prorate = False
             join_date = contract.employee_id.join_date
             resign_date = contract.employee_id.resign_date if contract.employee_id.resign_date else contract.date_end
             date_start = payslip.date_from
             date_to = payslip.date_to

             if ((date_start < join_date < date_to) or (join_date > date_to) or
                 (resign_date and date_start < resign_date < date_to) or
                 (resign_date and resign_date <= date_start)):
                 prorate = True

             if prorate:
                 schedule_prorate = contract.employee_id.department_id.schedule_prorate
                 schedule_prorate = schedule_prorate if schedule_prorate > 0 else 1
                 working_days = 0
                 for slip in payslip.worked_days_line_ids:
                     if slip.sequence == 1:
                         working_days = slip.number_of_days if slip.number_of_days else 0

                 # Adjust base amount proportionally for prorate
                 if working_days > 0 and schedule_prorate > 0:
                     prorate_factor = working_days / schedule_prorate
                     base_amount = base_amount * prorate_factor
                     base_amount = base_amount if base_amount < total_earnings + total_deductions else total_earnings + total_deductions

         # Calculate SP deduction
         result = -1 * (base_amount * payslip.sp_deduction_percentage / 100)
     ```
   - Appears on Payslip: Yes

When generating payslips for employees with active disciplinary actions that include salary deductions, the system will automatically apply the deduction based on the configured percentage.

## License

LGPL-3
