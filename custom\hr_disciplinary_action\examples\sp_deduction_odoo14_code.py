# SP Deduction Salary Rule - Odoo 14 Fixed Version
# Copy paste kode ini ke field "Python Code" di salary rule

# RECOMMENDED VERSION: Direct Calculation (No Database Issues)
if not payslip.has_sp_deduction:
    result = 0.0
else:
    # Calculate total earnings (Basic + Allowance)
    total_earnings = 0.0
    for line in payslip.line_ids:
        if line.category_id.name in ['Basic', 'Allowance']:
            total_earnings += line.total

    # Calculate total deductions (excluding SP deduction itself)
    total_deductions = 0.0
    for line in payslip.line_ids:
        if line.category_id.name == 'Deduction' and line.code != 'SP_DED':
            total_deductions += line.total

    # Base amount for SP calculation = Total Earnings - Other Deductions
    base_amount = total_earnings + total_deductions  # deductions are negative

    # Handle different employment types
    if contract.employment_state == 'part_time':
        # For part-time: calculate based on working days
        working_days = 0
        for slip in payslip.worked_days_line_ids:
            if slip.sequence == 1:
                working_days = slip.number_of_days if slip.number_of_days else 0

        # Adjust base amount proportionally for part-time
        if working_days > 0:
            base_amount = base_amount * working_days / 30
    else:
        # Check for prorate conditions
        prorate = False
        join_date = contract.employee_id.join_date
        resign_date = contract.employee_id.resign_date if contract.employee_id.resign_date else contract.date_end
        date_start = payslip.date_from
        date_to = payslip.date_to

        if ((date_start < join_date < date_to) or (join_date > date_to) or
            (resign_date and date_start < resign_date < date_to) or
            (resign_date and resign_date <= date_start)):
            prorate = True

        if prorate:
            schedule_prorate = contract.employee_id.department_id.schedule_prorate
            schedule_prorate = schedule_prorate if schedule_prorate > 0 else 1
            working_days = 0
            for slip in payslip.worked_days_line_ids:
                if slip.sequence == 1:
                    working_days = slip.number_of_days if slip.number_of_days else 0

            # Adjust base amount proportionally for prorate
            if working_days > 0 and schedule_prorate > 0:
                prorate_factor = working_days / schedule_prorate
                base_amount = base_amount * prorate_factor
                base_amount = base_amount if base_amount < total_earnings + total_deductions else total_earnings + total_deductions

    # Calculate SP deduction
    result = -1 * (base_amount * payslip.sp_deduction_percentage / 100)

# OPTION 2: Manual Calculation Version (if needed)
# Uncomment below if you want to calculate manually in salary rule

"""
if not payslip.has_sp_deduction:
    result = 0.0
else:
    # Calculate total earnings (Basic + Allowance)
    total_earnings = 0.0
    for line in payslip.line_ids:
        if line.category_id.name in ['Basic', 'Allowance']:
            total_earnings += line.total

    # Calculate total deductions (excluding SP deduction itself)
    total_deductions = 0.0
    for line in payslip.line_ids:
        if line.category_id.name == 'Deduction' and line.code != 'SP_DED':
            total_deductions += line.total

    # Base amount for SP calculation = Total Earnings - Other Deductions
    base_amount = total_earnings + total_deductions  # deductions are negative

    # Handle different employment types
    if contract.employment_state == 'part_time':
        # For part-time: calculate based on working days
        working_days = 0
        for slip in payslip.worked_days_line_ids:
            if slip.sequence == 1:
                working_days = slip.number_of_days if slip.number_of_days else 0

        # Adjust base amount proportionally for part-time
        if working_days > 0:
            base_amount = base_amount * working_days / 30
    else:
        # Check for prorate conditions
        prorate = False
        join_date = contract.employee_id.join_date
        resign_date = contract.employee_id.resign_date if contract.employee_id.resign_date else contract.date_end
        date_start = payslip.date_from
        date_to = payslip.date_to

        if ((date_start < join_date < date_to) or (join_date > date_to) or
            (resign_date and date_start < resign_date < date_to) or
            (resign_date and resign_date <= date_start)):
            prorate = True

        if prorate:
            schedule_prorate = contract.employee_id.department_id.schedule_prorate
            schedule_prorate = schedule_prorate if schedule_prorate > 0 else 1
            working_days = 0
            for slip in payslip.worked_days_line_ids:
                if slip.sequence == 1:
                    working_days = slip.number_of_days if slip.number_of_days else 0

            # Adjust base amount proportionally for prorate
            if working_days > 0 and schedule_prorate > 0:
                prorate_factor = working_days / schedule_prorate
                base_amount = base_amount * prorate_factor
                base_amount = base_amount if base_amount < total_earnings + total_deductions else total_earnings + total_deductions

    # Calculate SP deduction
    result = -1 * (base_amount * payslip.sp_deduction_percentage / 100)
"""
