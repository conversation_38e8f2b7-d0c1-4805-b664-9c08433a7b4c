<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Payslip Form View - Add SP Information -->
        <record id="view_hr_payslip_form_inherit_sp" model="ir.ui.view">
            <field name="name">hr.payslip.form.inherit.sp</field>
            <field name="model">hr.payslip</field>
            <field name="inherit_id" ref="om_hr_payroll.view_hr_payslip_form"/>
            <field name="arch" type="xml">
                <!-- Add SP information after employee_id -->
                <xpath expr="//field[@name='employee_id']" position="after">
                    <field name="has_sp_deduction" invisible="1"/>
                    <field name="sp_deduction_percentage" invisible="1"/>
                </xpath>

                <!-- Add SP information in a group -->
                <xpath expr="//group[@name='other_info']" position="after">
                    <group string="SP Deduction Information" attrs="{'invisible': [('has_sp_deduction', '=', False)]}">
                        <field name="disciplinary_action_ids" widget="many2many_tags" readonly="1"/>
                        <field name="sp_deduction_percentage" string="SP Deduction %" readonly="1"/>
                    </group>
                </xpath>
            </field>
        </record>

        <!-- Payslip Tree View - Add SP Indicator -->
        <record id="view_hr_payslip_tree_inherit_sp" model="ir.ui.view">
            <field name="name">hr.payslip.tree.inherit.sp</field>
            <field name="model">hr.payslip</field>
            <field name="inherit_id" ref="om_hr_payroll.view_hr_payslip_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='state']" position="before">
                    <field name="has_sp_deduction" string="Has SP" widget="boolean_toggle"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
