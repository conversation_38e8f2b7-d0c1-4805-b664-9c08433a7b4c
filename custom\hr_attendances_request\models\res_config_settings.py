from odoo import models, fields


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    is_limit_attendance_request = fields.Boolean(
        string='Batasi Pengajuan Absensi',
        config_parameter='hr_attendances_request.is_limit_attendance_request',
        default=False
    )

    limit_attendance_request = fields.Integer(
        string='<PERSON><PERSON> (hari setelah cut-off)',
        config_parameter='hr_attendances_request.limit_attendance_request',
        default=0
    )
