from odoo import models, fields, api, _
from datetime import date
from dateutil.relativedelta import relativedelta

class HrPayslip(models.Model):
    _inherit = 'hr.payslip'

    disciplinary_action_ids = fields.Many2many(
        'hr.disciplinary.action',
        string='Disciplinary Actions',
        compute='_compute_disciplinary_actions',
        store=True,
        help='Disciplinary actions affecting this payslip'
    )
    has_sp_deduction = fields.Boolean(
        string='Has SP Deduction',
        compute='_compute_disciplinary_actions',
        store=True,
        help='Indicates if this employee has active SP deduction'
    )
    sp_deduction_percentage = fields.Float(
        string='SP Deduction Percentage',
        compute='_compute_disciplinary_actions',
        store=True,
        help='Total percentage of SP deduction to be applied'
    )
    sp_deduction_amount = fields.Float(
        string='SP Deduction Amount',
        help='Calculated SP deduction amount',
        default=0.0
    )

    @api.depends('employee_id', 'date_from', 'date_to')
    def _compute_disciplinary_actions(self):
        """Compute active disciplinary actions for this payslip period"""
        for payslip in self:
            if not payslip.employee_id or not payslip.date_from or not payslip.date_to:
                payslip.disciplinary_action_ids = False
                payslip.has_sp_deduction = False
                payslip.sp_deduction_percentage = 0.0
                continue

            # Find active disciplinary actions for this employee during the payslip period
            domain = [
                ('employee_id', '=', payslip.employee_id.id),
                ('state', '=', 'approved'),
                ('effective_date', '<=', payslip.date_to),
                ('validity_end_date', '>=', payslip.date_from),
                ('additional_salary_deduction', '=', True),
                ('remaining_validity', '>', 0)
            ]

            actions = self.env['hr.disciplinary.action'].search(domain)
            payslip.disciplinary_action_ids = actions

            # Calculate total deduction percentage (in case there are multiple active SPs)
            total_percentage = 0.0
            for action in actions:
                total_percentage += action.salary_deduction_percentage

            payslip.has_sp_deduction = bool(actions)
            payslip.sp_deduction_percentage = min(total_percentage, 100.0)  # Cap at 100%

    def compute_sp_deduction_amount(self):
        """Compute SP deduction amount based on earnings and employment type"""
        self.ensure_one()
        if not self.has_sp_deduction or self.sp_deduction_percentage <= 0:
            self.sp_deduction_amount = 0.0
            return

        # Calculate total earnings (Basic + Allowance)
        total_earnings = 0.0
        for line in self.line_ids:
            if line.category_id.name in ['Basic', 'Allowance']:
                total_earnings += line.total

        # Calculate total deductions (excluding SP deduction itself)
        total_deductions = 0.0
        for line in self.line_ids:
            if line.category_id.name == 'Deduction' and line.code != 'SP_DED':
                total_deductions += line.total

        # Base amount for SP calculation = Total Earnings - Other Deductions
        base_amount = total_earnings + total_deductions  # deductions are negative

        # Handle different employment types
        if self.contract_id and self.contract_id.employment_state == 'part_time':
            # For part-time: calculate based on working days
            working_days = 0
            for slip in self.worked_days_line_ids:
                if slip.sequence == 1:
                    working_days = slip.number_of_days if slip.number_of_days else 0

            # Adjust base amount proportionally for part-time
            if working_days > 0:
                base_amount = base_amount * working_days / 30
        elif self.contract_id:
            # Check for prorate conditions
            prorate = self._check_prorate_conditions()

            if prorate:
                schedule_prorate = self.contract_id.employee_id.department_id.schedule_prorate
                schedule_prorate = schedule_prorate if schedule_prorate > 0 else 1
                working_days = 0
                for slip in self.worked_days_line_ids:
                    if slip.sequence == 1:
                        working_days = slip.number_of_days if slip.number_of_days else 0

                # Adjust base amount proportionally for prorate
                if working_days > 0 and schedule_prorate > 0:
                    prorate_factor = working_days / schedule_prorate
                    base_amount = base_amount * prorate_factor
                    base_amount = min(base_amount, total_earnings + total_deductions)

        # Calculate SP deduction amount
        self.sp_deduction_amount = base_amount * self.sp_deduction_percentage / 100

    def _check_prorate_conditions(self):
        """Check if prorate conditions are met for this payslip"""
        self.ensure_one()
        if not self.contract_id:
            return False

        prorate = False
        join_date = self.contract_id.employee_id.join_date
        resign_date = self.contract_id.employee_id.resign_date or self.contract_id.date_end
        date_start = self.date_from
        date_to = self.date_to

        if ((date_start < join_date < date_to) or (join_date > date_to) or
            (resign_date and date_start < resign_date < date_to) or
            (resign_date and resign_date <= date_start)):
            prorate = True

        return prorate

    def compute_sheet(self):
        """Override compute_sheet to calculate SP deduction amount after lines are computed"""
        res = super(HrPayslip, self).compute_sheet()

        # Calculate SP deduction amount after all lines are computed
        for payslip in self:
            payslip.compute_sp_deduction_amount()

        return res

    def action_payslip_done(self):
        res = super(HrPayslip, self).action_payslip_done()

        # Update remaining validity for disciplinary actions
        for payslip in self:
            for action in payslip.disciplinary_action_ids:
                # Recalculate remaining validity after payslip is done
                action._compute_remaining_validity()

        return res

    def write(self, vals):
        """Override to handle hide/unhide payslip and update disciplinary action remaining validity"""
        # Check if view_type_slip is being changed
        if 'view_type_slip' in vals:
            for payslip in self:
                # Only process if there are disciplinary actions and the view type is changing
                if payslip.disciplinary_action_ids and (
                    (vals['view_type_slip'] == 'hide' and payslip.view_type_slip == 'unhide') or
                    (vals['view_type_slip'] == 'unhide' and payslip.view_type_slip == 'hide')
                ):
                    for action in payslip.disciplinary_action_ids:
                        # If changing from unhide to hide, increase remaining validity
                        if vals['view_type_slip'] == 'hide' and payslip.view_type_slip == 'unhide':
                            # Only increase if it's still within the original validity period
                            if action.effective_date and action.salary_deduction_validity:
                                # Calculate the original end date
                                original_end_date = fields.Date.from_string(action.effective_date) + relativedelta(months=action.salary_deduction_validity)
                                today = fields.Date.from_string(fields.Date.today())

                                # Only increase if we're still within the original validity period
                                if original_end_date >= today:
                                    action.sudo().write({
                                        'remaining_validity': action.remaining_validity + 1
                                    })
                        # If changing from hide to unhide, decrease remaining validity
                        elif vals['view_type_slip'] == 'unhide' and payslip.view_type_slip == 'hide':
                            # Only decrease if there's remaining validity
                            if action.remaining_validity > 0:
                                action.sudo().write({
                                    'remaining_validity': action.remaining_validity - 1
                                })

        return super(HrPayslip, self).write(vals)
