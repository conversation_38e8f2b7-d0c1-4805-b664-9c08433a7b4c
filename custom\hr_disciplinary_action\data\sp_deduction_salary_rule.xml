<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- SP Deduction Salary Rule for Odoo 14 -->
        <record id="hr_salary_rule_sp_deduction" model="hr.salary.rule">
            <field name="name">SP Deduction</field>
            <field name="code">SP_DED</field>
            <field name="sequence">200</field>
            <field name="category_id" ref="om_hr_payroll.DED"/>
            <field name="condition_select">python</field>
            <field name="condition_python">result = payslip.has_sp_deduction</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">
# SP Deduction Calculation - Odoo 14 Compatible
# Automatically detects if employee has active disciplinary actions

# Check if employee has SP deduction
if not payslip.has_sp_deduction:
    result = 0.0
else:
    # Use pre-calculated amount from payslip model
    result = -1 * payslip.sp_deduction_amount
            </field>
            <field name="appears_on_payslip" eval="True"/>
            <field name="active" eval="True"/>
        </record>

    </data>
</odoo>
